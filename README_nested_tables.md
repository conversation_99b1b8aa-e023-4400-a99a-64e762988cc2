# DocX 嵌套表格处理工具

## 功能描述

这个工具用于处理 Microsoft Word (.docx) 文档中的嵌套表格问题。它可以：

1. **检测嵌套表格**：自动识别表格单元格中嵌套的完整表格
2. **提取嵌套内容**：将嵌套表格的所有内容和格式提取出来
3. **平铺表格**：将嵌套表格作为独立表格插入到文档中
4. **保持格式**：尽可能保留原有的表格格式和文本内容
5. **清理原表格**：从原单元格中移除嵌套表格，保留其他内容

## 使用方法

### 基本用法

```python
from test1 import process_docx_nested_tables

# 处理包含嵌套表格的文档
success = process_docx_nested_tables("input.docx", "output.docx")

if success:
    print("处理完成！")
else:
    print("处理失败！")
```

### 高级用法

```python
from test1 import NestedTableProcessor

# 创建处理器实例
processor = NestedTableProcessor()

# 处理文档
success = processor.process_document("input.docx", "output.docx")

# 查看处理结果
print(f"共提取了 {len(processor.extracted_tables)} 个嵌套表格")

for i, table_info in enumerate(processor.extracted_tables):
    row, col = table_info['source_position']
    print(f"表格 {i+1}: 来自第 {row+1} 行第 {col+1} 列")
```

## 处理流程

1. **文档读取**：使用 python-docx 读取输入的 .docx 文件
2. **表格遍历**：遍历文档中的所有表格
3. **嵌套检测**：检查每个表格单元格是否包含嵌套表格
4. **内容提取**：提取嵌套表格的完整内容和格式
5. **表格插入**：在原表格后插入提取的嵌套表格
6. **内容清理**：从原单元格中移除嵌套表格
7. **文档保存**：保存处理后的新文档

## 技术特点

### 支持的功能
- ✅ 检测单元格中的嵌套表格
- ✅ 提取嵌套表格的文本内容
- ✅ 保持基本的表格格式
- ✅ 添加来源说明
- ✅ 递归处理多层嵌套
- ✅ 错误处理和异常恢复

### 格式保持
- 单元格文本内容
- 基本的单元格对齐方式
- 表格结构（行列数）
- 段落格式

### 限制说明
- 复杂的表格样式可能无法完全保持
- 图片和其他嵌入对象需要特殊处理
- 表格边框样式可能需要手动调整

## 依赖要求

```bash
pip install python-docx
```

## 示例场景

### 输入文档结构
```
主表格:
┌─────────────┬─────────────┐
│ 普通文本    │ 嵌套表格:   │
│             │ ┌─────┬─────┐│
│             │ │ A   │ B   ││
│             │ ├─────┼─────┤│
│             │ │ C   │ D   ││
│             │ └─────┴─────┘│
└─────────────┴─────────────┘
```

### 输出文档结构
```
主表格:
┌─────────────┬─────────────┐
│ 普通文本    │ (清理后)    │
└─────────────┴─────────────┘

以下表格来自原表格第1行第2列：
┌─────┬─────┐
│ A   │ B   │
├─────┼─────┤
│ C   │ D   │
└─────┴─────┘
```

## 错误处理

工具包含完善的错误处理机制：

- 文件读取错误
- 表格结构异常
- 格式复制失败
- XML 操作错误

所有错误都会被捕获并记录，不会中断整个处理流程。

## 注意事项

1. **备份原文件**：处理前请备份原始文档
2. **测试验证**：处理后请检查输出文档的内容和格式
3. **复杂表格**：对于非常复杂的嵌套结构，可能需要手动调整
4. **性能考虑**：大型文档处理可能需要较长时间

## 扩展功能

可以根据需要扩展以下功能：

- 图片和对象处理
- 更复杂的格式保持
- 批量文档处理
- 自定义输出格式
- 表格合并选项

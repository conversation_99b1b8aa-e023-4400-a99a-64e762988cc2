# -*- coding: utf-8 -*-
"""
@File  : test2.py
@Author: <PERSON>
@Date  : 2025/9/5 14:55
@Desc  : 
"""
# test1.py

import docx
from docx.document import Document as _Document
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.table import _Cell, Table, _Row
from docx.text.paragraph import Paragraph
import copy


def copy_cell_content(source_cell: _Cell, target_cell: _Cell):
    """
    将源单元格的所有内容（段落和表格）及其格式复制到目标单元格。

    Args:
        source_cell: 内容来源单元格。
        target_cell: 要将内容复制到的目标单元格。
    """
    # 清空目标单元格
    target_cell._element.clear_content()

    # 复制源单元格的元素
    for element in source_cell._element:
        # 使用copy.deepcopy确保所有子元素都被复制
        new_element = copy.deepcopy(element)
        target_cell._element.append(new_element)


def process_nested_tables_in_docx(input_path: str, output_path: str):
    """
    处理docx文档，将表格中嵌套的表格平铺展开。

    Args:
        input_path (str): 输入的docx文件路径。
        output_path (str): 处理后输出的docx文件路径。
    """
    doc = docx.Document(input_path)

    print(f"开始处理文档 '{input_path}'...")
    print(f"共找到 {len(doc.tables)} 个表格。")

    # 遍历文档中的每一个表格
    for i, table in enumerate(doc.tables):
        print(f"\n正在处理第 {i + 1} 个表格...")

        # 用于存储最终表格所有行的数据
        # 每一行的数据格式是 [cell_1_element, cell_2_element, ...]
        final_rows_data = []

        # 标记是否在此表格中进行了平铺操作
        has_flattened = False

        # 遍历表格的每一行
        for row in table.rows:
            nested_table_info = None

            # 查找当前行是否有单元格包含嵌套表格
            for cell_idx, cell in enumerate(row.cells):
                # docx中，单元格内的表格也是一个Table对象
                if len(cell.tables) > 0:
                    has_flattened = True
                    # 提取单元格内的段落（嵌套表格前的内容）和第一个嵌套表格
                    # 注意：这里假设一个单元格最多只有一个需要处理的嵌套表格
                    prefix_paragraphs = [
                        copy.deepcopy(p._element) for p in cell.paragraphs
                    ]
                    nested_table = cell.tables[0]

                    nested_table_info = {
                        "cell_idx": cell_idx,
                        "prefix_paragraphs": prefix_paragraphs,
                        "nested_table": nested_table
                    }
                    break  # 找到后即跳出，处理这一行

            # --- 根据是否找到嵌套表格，生成新的行数据 ---

            if nested_table_info:
                # 情况一：当前行包含嵌套表格，需要平铺
                print(f"  - 在第 {len(final_rows_data) + 1} 行找到嵌套表格，开始平铺...")

                # 获取包含嵌套表格的单元格的索引
                cell_idx_with_nested_table = nested_table_info["cell_idx"]

                # 遍历嵌套表格的每一行，生成新的平铺行
                for nested_row in nested_table_info["nested_table"].rows:
                    new_row_cells = []

                    # 1. 复制原始行在嵌套表格之前的所有单元格
                    for j in range(cell_idx_with_nested_table):
                        new_row_cells.append(row.cells[j])

                    # 2. 处理嵌套表格的单元格，将其平铺出来
                    for nested_cell_idx, nested_cell in enumerate(nested_row.cells):
                        # 创建一个临时的“源单元格”来组合内容
                        temp_doc = docx.Document()
                        temp_cell = temp_doc.add_table(1, 1).cell(0, 0)
                        temp_cell._element.clear_content()

                        # 如果是嵌套表格的第一个单元格，需要把前置段落也加进去
                        if nested_cell_idx == 0:
                            for p_elm in nested_table_info["prefix_paragraphs"]:
                                temp_cell._element.append(copy.deepcopy(p_elm))

                        # 将嵌套单元格的内容复制过来
                        for element in nested_cell._element:
                            temp_cell._element.append(copy.deepcopy(element))

                        new_row_cells.append(temp_cell)

                    # 3. 复制原始行在嵌套表格之后的所有单元格
                    # 注意：这部分逻辑假设嵌套表格平铺后，会占据原始表格后续的列
                    # 如果不需要，可以注释掉这部分
                    start_idx_after_nested = cell_idx_with_nested_table + 1
                    for j in range(start_idx_after_nested, len(row.cells)):
                        new_row_cells.append(row.cells[j])

                    final_rows_data.append(new_row_cells)

            else:
                # 情况二：当前行是普通行，直接保存
                final_rows_data.append(row.cells)

        # --- 如果进行了平铺操作，则使用 final_rows_data 重建表格 ---
        if has_flattened:
            print(f"  - 表格处理完成，开始重建...")

            # 1. 清空原表格所有行
            # 从后往前删除，避免索引问题
            for row in reversed(table.rows):
                # _tbl是表格的XML元素，tr是行的XML元素
                table._tbl.remove(row._tr)

            # 2. 根据 final_rows_data 重新添加行和内容
            for row_data in final_rows_data:
                new_row = table.add_row()
                # 确保新行有足够的单元格
                while len(new_row.cells) < len(row_data):
                    # add_row() 可能只创建了和之前列数相同的单元格
                    # 实际操作中，如果列数可能增加，处理会更复杂
                    # 这里我们假设列数不变或通过平铺增加
                    # python-docx不直接支持添加单元格，但我们可以通过XML操作
                    # 为简化，这里假设列数匹配
                    pass

                # 复制内容
                for cell_idx, source_cell in enumerate(row_data):
                    if cell_idx < len(new_row.cells):
                        target_cell = new_row.cells[cell_idx]
                        copy_cell_content(source_cell, target_cell)
            print(f"  - 第 {i + 1} 个表格重建完成。")
        else:
            print(f"  - 第 {i + 1} 个表格没有发现需要处理的嵌套表格。")

    # 保存修改后的文档
    try:
        doc.save(output_path)
        print(f"\n处理完成！新文档已保存至 '{output_path}'")
    except Exception as e:
        print(f"\n保存文件时出错: {e}")


def create_sample_docx(filename="test1.docx"):
    """创建一个包含嵌套表格的示例docx文件用于测试。"""
    doc = docx.Document()
    doc.add_heading('文档标题', 0)
    doc.add_paragraph('这是一个用于测试嵌套表格平铺功能的示例文档。')

    # 添加一个表格
    table = doc.add_table(rows=3, cols=3)
    table.style = 'Table Grid'

    # --- 填充第一行 (普通行) ---
    table.cell(0, 0).text = 'ID'
    table.cell(0, 1).text = '姓名'
    table.cell(0, 2).text = '备注'

    # --- 填充第二行 (包含嵌套表格的行) ---
    table.cell(1, 0).text = '101'
    table.cell(1, 1).text = '张三'

    # 这是关键单元格
    cell_with_nested_table = table.cell(1, 2)
    p1 = cell_with_nested_table.add_paragraph('以下是该用户的详细订单：')
    p1.add_run('（重要）').bold = True
    cell_with_nested_table.add_paragraph('请仔细核对。')

    # 在单元格内添加一个嵌套表格
    nested_table = cell_with_nested_table.add_table(rows=2, cols=2)
    nested_table.cell(0, 0).text = '订单号'
    nested_table.cell(0, 1).text = '金额'
    nested_table.cell(1, 0).text = 'ORD-001'
    nested_table.cell(1, 1).text = '¥500'
    # 再加一行，测试多行平铺
    new_nested_row = nested_table.add_row().cells
    new_nested_row[0].text = 'ORD-002'
    new_nested_row[1].text = '¥1200'

    # --- 填充第三行 (普通行) ---
    table.cell(2, 0).text = '102'
    table.cell(2, 1).text = '李四'
    table.cell(2, 2).text = '无特殊备注。'

    doc.add_paragraph('\n文档末尾的其它内容。')

    try:
        doc.save(filename)
        print(f"示例文件 '{filename}' 创建成功。")
    except Exception as e:
        print(f"创建示例文件时出错: {e}")


if __name__ == '__main__':
    # 1. 创建一个名为 test1.docx 的示例文档
    # create_sample_docx(filename="test1.docx")

    # 2. 处理这个文档，并生成一个新文档
    input_file = "彩云湖污水处理厂提标改造工程.docx"
    output_file = "彩云湖污水处理厂提标改造工程_out2.docx"
    process_nested_tables_in_docx(input_file, output_file)
# -*- coding: utf-8 -*-

import os
from langchain_community.document_loaders import UnstructuredPDFLoader



import pandas as pd

data = pd.DataFrame({1:[2]})


print(111)

loader = UnstructuredPDFLoader(
    "/mnt/c/Users/<USER>/Desktop/Doc1.pdf",
    mode="elements",   # single elements
    strategy="hi_res",  # ocr_only  fast
    infer_table_structure=True, # 启用表格结构推断
    languages=["chi_sim", "eng"]  # 指定语言为简体中文和英文
)
print(222)
documents = loader.load()
print("文档加载完成，Unstructured解析了 {} 页。".format(len(documents)))

for i, doc in enumerate(documents[:5]):
        print(f"--- Element {i} ---")
        print(doc.page_content)
        print("-" * 20)



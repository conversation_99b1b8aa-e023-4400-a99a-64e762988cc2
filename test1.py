# -*- coding: utf-8 -*-
"""
@File  : test1.py
@Author: <PERSON>
@Date  : 2025/9/5 14:22
@Desc  : 处理docx文档中的嵌套表格，将嵌套表格平铺出来
"""

from docx import Document
from docx.table import Table, _Cell
from docx.text.paragraph import Paragraph
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.shared import Inches
import copy
from typing import List, Tuple, Optional


class NestedTableProcessor:
    """处理docx文档中嵌套表格的类"""

    def __init__(self):
        self.extracted_tables = []  # 存储提取的嵌套表格信息

    def process_document(self, input_path: str, output_path: str) -> bool:
        """
        处理包含嵌套表格的docx文档

        Args:
            input_path: 输入文档路径
            output_path: 输出文档路径

        Returns:
            bool: 处理是否成功
        """
        try:
            # 读取文档
            document = Document(input_path)

            # 查找并处理嵌套表格
            self._process_nested_tables(document)

            # 保存处理后的文档
            document.save(output_path)

            print(f"文档处理完成，共处理了 {len(self.extracted_tables)} 个嵌套表格")
            return True

        except Exception as e:
            print(f"处理文档时出错: {str(e)}")
            return False

    def _process_nested_tables(self, document: Document):
        """处理文档中的所有嵌套表格"""
        # 获取文档中的所有表格
        tables = document.tables.copy()  # 复制列表，因为我们会修改原列表

        for i, table in enumerate(tables):
            nested_tables_info = self._find_nested_tables_in_table(table)

            if nested_tables_info:
                # 在当前表格后插入提取的嵌套表格
                self._insert_extracted_tables(document, table, nested_tables_info)

                # 清理原表格中的嵌套表格
                self._clean_nested_tables_from_cells(nested_tables_info)

    def _find_nested_tables_in_table(self, table: Table) -> List[Tuple[_Cell, Table, int, int]]:
        """
        在表格中查找嵌套表格

        Args:
            table: 要检查的表格

        Returns:
            List[Tuple[_Cell, Table, int, int]]: 包含嵌套表格信息的列表
            每个元组包含：(单元格, 嵌套表格, 行索引, 列索引)
        """
        nested_tables_info = []

        for row_idx, row in enumerate(table.rows):
            for col_idx, cell in enumerate(row.cells):
                # 检查单元格中是否有嵌套表格
                nested_tables = self._get_tables_in_cell(cell)

                for nested_table in nested_tables:
                    nested_tables_info.append((cell, nested_table, row_idx, col_idx))

        return nested_tables_info

    def _get_tables_in_cell(self, cell: _Cell) -> List[Table]:
        """
        获取单元格中的所有表格

        Args:
            cell: 要检查的单元格

        Returns:
            List[Table]: 单元格中的表格列表
        """
        tables = []

        # 通过XML元素查找嵌套表格
        for element in cell._element:
            if element.tag.endswith('tbl'):  # 表格元素
                # 创建Table对象
                table = Table(element, cell.part)
                tables.append(table)

        return tables

    def _insert_extracted_tables(self, document: Document, parent_table: Table,
                                nested_tables_info: List[Tuple[_Cell, Table, int, int]]):
        """
        在文档中插入提取的嵌套表格

        Args:
            document: 文档对象
            parent_table: 父表格
            nested_tables_info: 嵌套表格信息列表
        """
        # 找到父表格在文档中的位置
        parent_table_element = parent_table._element
        parent_element = parent_table_element.getparent()

        # 获取父表格的索引
        table_index = list(parent_element).index(parent_table_element)

        # 在父表格后插入提取的表格
        insert_index = table_index + 1

        for cell, nested_table, row_idx, col_idx in nested_tables_info:
            # 复制嵌套表格
            new_table = self._copy_table(document, nested_table, insert_index)

            # 添加说明段落
            explanation_p = document.add_paragraph()
            explanation_p.text = f"以下表格来自原表格第{row_idx+1}行第{col_idx+1}列："

            # 移动到正确位置
            explanation_element = explanation_p._element
            parent_element.insert(insert_index, explanation_element)
            insert_index += 1

            # 移动表格到正确位置
            new_table_element = new_table._element
            parent_element.insert(insert_index, new_table_element)
            insert_index += 1

            # 添加空行分隔
            separator_p = document.add_paragraph()
            separator_element = separator_p._element
            parent_element.insert(insert_index, separator_element)
            insert_index += 1

            # 记录提取的表格信息
            self.extracted_tables.append({
                'source_position': (row_idx, col_idx),
                'table': new_table
            })

    def _copy_table(self, document: Document, source_table: Table, insert_index: int) -> Table:
        """
        复制表格及其内容和格式

        Args:
            document: 目标文档
            source_table: 源表格
            insert_index: 插入位置索引

        Returns:
            Table: 复制的新表格
        """
        # 获取源表格的行数和列数
        rows_count = len(source_table.rows)
        cols_count = len(source_table.columns) if source_table.rows else 0

        if rows_count == 0 or cols_count == 0:
            # 创建一个1x1的空表格
            new_table = document.add_table(1, 1)
            new_table.cell(0, 0).text = "[空表格]"
            return new_table

        # 创建新表格
        new_table = document.add_table(rows_count, cols_count)

        # 复制表格内容和基本格式
        for row_idx, source_row in enumerate(source_table.rows):
            for col_idx, source_cell in enumerate(source_row.cells):
                if row_idx < len(new_table.rows) and col_idx < len(new_table.rows[row_idx].cells):
                    target_cell = new_table.cell(row_idx, col_idx)

                    # 复制文本内容（排除嵌套表格）
                    cell_text = self._extract_text_from_cell(source_cell)
                    target_cell.text = cell_text

                    # 复制基本格式
                    self._copy_cell_format(source_cell, target_cell)

        return new_table

    def _extract_text_from_cell(self, cell: _Cell) -> str:
        """
        从单元格中提取文本内容，排除嵌套表格

        Args:
            cell: 源单元格

        Returns:
            str: 提取的文本内容
        """
        text_parts = []

        # 遍历单元格中的段落
        for paragraph in cell.paragraphs:
            if paragraph.text.strip():
                text_parts.append(paragraph.text.strip())

        return '\n'.join(text_parts)

    def _copy_cell_format(self, source_cell: _Cell, target_cell: _Cell):
        """
        复制单元格格式

        Args:
            source_cell: 源单元格
            target_cell: 目标单元格
        """
        try:
            # 复制单元格宽度
            if hasattr(source_cell, 'width') and source_cell.width:
                target_cell.width = source_cell.width

            # 复制垂直对齐
            if hasattr(source_cell, 'vertical_alignment'):
                target_cell.vertical_alignment = source_cell.vertical_alignment

            # 复制段落格式
            if source_cell.paragraphs and target_cell.paragraphs:
                source_para = source_cell.paragraphs[0]
                target_para = target_cell.paragraphs[0]

                # 复制段落对齐
                if hasattr(source_para, 'alignment'):
                    target_para.alignment = source_para.alignment

        except Exception as e:
            # 格式复制失败时继续处理，不中断程序
            print(f"复制单元格格式时出错: {str(e)}")

    def _clean_nested_tables_from_cells(self, nested_tables_info: List[Tuple[_Cell, Table, int, int]]):
        """
        从原单元格中清理嵌套表格

        Args:
            nested_tables_info: 嵌套表格信息列表
        """
        for cell, nested_table, row_idx, col_idx in nested_tables_info:
            self._remove_table_from_cell(cell, nested_table)

    def _remove_table_from_cell(self, cell: _Cell, table_to_remove: Table):
        """
        从单元格中移除指定的表格

        Args:
            cell: 单元格
            table_to_remove: 要移除的表格
        """
        try:
            # 获取要移除的表格元素
            table_element = table_to_remove._element

            # 从单元格中移除表格元素
            if table_element.getparent() is not None:
                table_element.getparent().remove(table_element)

        except Exception as e:
            print(f"移除嵌套表格时出错: {str(e)}")


def process_docx_nested_tables(input_path: str, output_path: str) -> bool:
    """
    处理docx文档中的嵌套表格，将其平铺出来

    Args:
        input_path: 输入文档路径
        output_path: 输出文档路径

    Returns:
        bool: 处理是否成功
    """
    processor = NestedTableProcessor()
    return processor.process_document(input_path, output_path)


def main():
    """主函数，用于测试"""
    input_file = "彩云湖污水处理厂提标改造工程.docx"  # 输入文件路径
    output_file = "彩云湖污水处理厂提标改造工程_out.docx"  # 输出文件路径

    print("开始处理docx文档中的嵌套表格...")

    success = process_docx_nested_tables(input_file, output_file)

    if success:
        print(f"处理完成！结果已保存到: {output_file}")
    else:
        print("处理失败，请检查输入文件和错误信息。")


if __name__ == "__main__":
    main()
